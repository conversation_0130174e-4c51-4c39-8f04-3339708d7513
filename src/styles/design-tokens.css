/**
 * Syndicaps Design Tokens
 * 
 * Centralized design system tokens for consistent styling
 * across the entire profile redesign implementation.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @priority HIGH
 */

:root {
  /* ===== COLOR SYSTEM ===== */
  
  /* Primary Brand Colors */
  --color-primary: #6366f1;
  --color-primary-50: #eef2ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #6366f1;
  --color-primary-600: #4f46e5;
  --color-primary-700: #4338ca;
  --color-primary-800: #3730a3;
  --color-primary-900: #312e81;

  /* Accent Colors (Purple) */
  --color-accent: #8b5cf6;
  --color-accent-50: #faf5ff;
  --color-accent-100: #f3e8ff;
  --color-accent-200: #e9d5ff;
  --color-accent-300: #d8b4fe;
  --color-accent-400: #c084fc;
  --color-accent-500: #a855f7;
  --color-accent-600: #9333ea;
  --color-accent-700: #7c3aed;
  --color-accent-800: #6b21a8;
  --color-accent-900: #581c87;

  /* Background Colors */
  --color-background: #030712;
  --color-surface: #1f2937;
  --color-surface-hover: #374151;
  --color-surface-active: #4b5563;
  --color-card: #1f2937;
  --color-card-hover: #374151;

  /* Border Colors */
  --color-border: #374151;
  --color-border-light: #4b5563;
  --color-border-accent: #8b5cf6;
  --color-border-focus: #a855f7;

  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #d1d5db;
  --color-text-muted: #9ca3af;
  --color-text-disabled: #6b7280;
  --color-text-accent: #c084fc;
  --color-text-link: #a855f7;

  /* Status Colors */
  --color-success: #10b981;
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-success-border: rgba(16, 185, 129, 0.3);
  
  --color-warning: #f59e0b;
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-warning-border: rgba(245, 158, 11, 0.3);
  
  --color-error: #ef4444;
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-error-border: rgba(239, 68, 68, 0.3);
  
  --color-info: #3b82f6;
  --color-info-bg: rgba(59, 130, 246, 0.1);
  --color-info-border: rgba(59, 130, 246, 0.3);

  /* ===== MEMBERSHIP TIER COLORS ===== */

  /* Bronze Tier - Metallic Bronze */
  --color-tier-bronze: #CD7F32;
  --color-tier-bronze-bg: rgba(205, 127, 50, 0.15);
  --color-tier-bronze-border: rgba(205, 127, 50, 0.4);
  --color-tier-bronze-text: #F4A460;

  /* Silver Tier - Metallic Silver */
  --color-tier-silver: #C0C0C0;
  --color-tier-silver-bg: rgba(192, 192, 192, 0.15);
  --color-tier-silver-border: rgba(192, 192, 192, 0.4);
  --color-tier-silver-text: #E5E5E5;

  /* Gold Tier - Metallic Gold */
  --color-tier-gold: #FFD700;
  --color-tier-gold-bg: rgba(255, 215, 0, 0.15);
  --color-tier-gold-border: rgba(255, 215, 0, 0.4);
  --color-tier-gold-text: #FFF8DC;

  /* Platinum Tier - Metallic Platinum */
  --color-tier-platinum: #E5E4E2;
  --color-tier-platinum-bg: rgba(229, 228, 226, 0.15);
  --color-tier-platinum-border: rgba(229, 228, 226, 0.4);
  --color-tier-platinum-text: #F5F5F5;

  /* Diamond Tier - Crystal Diamond */
  --color-tier-diamond: #B9F2FF;
  --color-tier-diamond-bg: rgba(185, 242, 255, 0.15);
  --color-tier-diamond-border: rgba(185, 242, 255, 0.4);
  --color-tier-diamond-text: #E0F7FF;

  /* ===== TYPOGRAPHY SYSTEM ===== */
  
  /* Font Families */
  --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'Fira Code', 'Consolas', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Font Weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* ===== SPACING SYSTEM ===== */
  
  /* Base spacing unit: 4px */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* Component specific spacing */
  --spacing-card: var(--spacing-6);
  --spacing-section: var(--spacing-8);
  --spacing-page: var(--spacing-4);

  /* ===== TOUCH TARGET SYSTEM ===== */
  
  /* Minimum touch targets for accessibility */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
  --touch-target-xl: 64px;

  /* Touch spacing */
  --touch-spacing-tight: 8px;
  --touch-spacing-normal: 12px;
  --touch-spacing-loose: 16px;

  /* ===== BORDER RADIUS SYSTEM ===== */
  
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* ===== SHADOW SYSTEM ===== */
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Focus shadows */
  --shadow-focus: 0 0 0 3px rgba(168, 85, 247, 0.5);
  --shadow-focus-accent: 0 0 0 3px rgba(139, 92, 246, 0.5);

  /* ===== ANIMATION SYSTEM ===== */
  
  /* Durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;

  /* Easing functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* ===== Z-INDEX SYSTEM ===== */
  
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ===== BREAKPOINT SYSTEM ===== */
  
  /* Mobile-first breakpoints */
  --breakpoint-sm: 640px;   /* Small mobile */
  --breakpoint-md: 768px;   /* Tablet */
  --breakpoint-lg: 1024px;  /* Desktop */
  --breakpoint-xl: 1280px;  /* Large desktop */
  --breakpoint-2xl: 1536px; /* Extra large */
}

/* ===== ACCESSIBILITY TOKENS ===== */

/* High contrast mode tokens */
@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #ffffff;
    --color-text-secondary: #ffffff;
    --color-border: #ffffff;
    --color-border-light: #ffffff;
  }
}

/* Reduced motion tokens */
@media (prefers-reduced-motion: reduce) {
  :root {
    --duration-fast: 0ms;
    --duration-normal: 0ms;
    --duration-slow: 0ms;
    --duration-slower: 0ms;
  }
}

/* Dark mode adjustments (default is dark) */
@media (prefers-color-scheme: light) {
  :root {
    /* Light mode overrides would go here if needed */
    /* Currently keeping dark theme as default */
  }
}

/* ===== UTILITY CLASSES ===== */

/* Touch targets */
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  position: relative;
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable);
  min-width: var(--touch-target-comfortable);
}

.touch-target-large {
  min-height: var(--touch-target-large);
  min-width: var(--touch-target-large);
}

.touch-target-sm {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--spacing-2);
}

/* Touch spacing */
.touch-spacing {
  margin: var(--touch-spacing-normal);
}

.touch-spacing-tight {
  margin: var(--touch-spacing-tight);
}

.touch-spacing-loose {
  margin: var(--touch-spacing-loose);
}

/* Focus states */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--color-accent-500);
  outline-offset: 2px;
}

.focus-ring-accent {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring-accent:focus {
  outline: 2px solid var(--color-accent-400);
  outline-offset: 2px;
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-accent-600);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
}

.skip-to-content:focus {
  top: 6px;
}

/* High contrast borders */
@media (prefers-contrast: high) {
  .card,
  .button,
  .input {
    border: 2px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== COMPONENT TOKEN OVERRIDES ===== */

/* Button variants */
.btn-primary {
  background-color: var(--color-accent-600);
  color: var(--color-text-primary);
  border: 1px solid var(--color-accent-600);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  min-height: var(--touch-target-min);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.btn-primary:hover {
  background-color: var(--color-accent-700);
  border-color: var(--color-accent-700);
}

.btn-primary:focus {
  box-shadow: var(--shadow-focus-accent);
}

.btn-secondary {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  min-height: var(--touch-target-min);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.btn-secondary:hover {
  background-color: var(--color-surface-hover);
  border-color: var(--color-border-light);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-text-accent);
  border: 1px solid var(--color-accent-600);
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  min-height: var(--touch-target-min);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.btn-outline:hover {
  background-color: var(--color-accent-600);
  color: var(--color-text-primary);
}

/* Card variants */
.card-base {
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-card);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  background-color: var(--color-card-hover);
  border-color: var(--color-border-light);
  box-shadow: var(--shadow-md);
}

.card-highlight {
  border-color: var(--color-accent-600);
  background-color: rgba(139, 92, 246, 0.05);
}

/* Input variants */
.input-base {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--color-text-primary);
  min-height: var(--touch-target-min);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.input-base:focus {
  outline: none;
  border-color: var(--color-accent-500);
  box-shadow: var(--shadow-focus-accent);
}

.input-base::placeholder {
  color: var(--color-text-muted);
}

/* ===== MEMBERSHIP TIER UTILITY CLASSES ===== */

/* Bronze Tier Classes */
.tier-bronze-text {
  color: var(--color-tier-bronze-text);
}

.tier-bronze-bg {
  background-color: var(--color-tier-bronze-bg);
}

.tier-bronze-border {
  border-color: var(--color-tier-bronze-border);
}

/* Silver Tier Classes */
.tier-silver-text {
  color: var(--color-tier-silver-text);
}

.tier-silver-bg {
  background-color: var(--color-tier-silver-bg);
}

.tier-silver-border {
  border-color: var(--color-tier-silver-border);
}

/* Gold Tier Classes */
.tier-gold-text {
  color: var(--color-tier-gold-text);
}

.tier-gold-bg {
  background-color: var(--color-tier-gold-bg);
}

.tier-gold-border {
  border-color: var(--color-tier-gold-border);
}

/* Platinum Tier Classes */
.tier-platinum-text {
  color: var(--color-tier-platinum-text);
}

.tier-platinum-bg {
  background-color: var(--color-tier-platinum-bg);
}

.tier-platinum-border {
  border-color: var(--color-tier-platinum-border);
}

/* Diamond Tier Classes */
.tier-diamond-text {
  color: var(--color-tier-diamond-text);
}

.tier-diamond-bg {
  background-color: var(--color-tier-diamond-bg);
}

.tier-diamond-border {
  border-color: var(--color-tier-diamond-border);
}