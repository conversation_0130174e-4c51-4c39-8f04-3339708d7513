/**
 * Gaming/Tech Enthusiast Theme Enhancements
 * 
 * Enhanced design system with neon accents, tech-inspired elements,
 * and improved visual hierarchy for the Syndicaps gaming theme.
 * 
 * <AUTHOR> Team
 */

/* ===== GAMING THEME VARIABLES ===== */
:root {
  /* Neon accent colors */
  --neon-cyan: #00ffff;
  --neon-purple: #8b5cf6;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-orange: #ff6600;
  --neon-blue: #0066ff;
  
  /* Gaming gradients */
  --gradient-cyber: linear-gradient(135deg, #8b5cf6 0%, #00ffff 50%, #8b5cf6 100%);
  --gradient-neon: linear-gradient(45deg, #ff00ff 0%, #00ffff 50%, #ff00ff 100%);
  --gradient-tech: linear-gradient(135deg, #1e293b 0%, #334155 50%, #1e293b 100%);
  --gradient-glow: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(0, 255, 255, 0.2) 100%);
  
  /* Enhanced shadows */
  --shadow-neon: 0 0 20px rgba(139, 92, 246, 0.5);
  --shadow-cyber: 0 0 30px rgba(0, 255, 255, 0.3);
  --shadow-tech: 0 4px 20px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 40px rgba(139, 92, 246, 0.2);
  
  /* Tech borders */
  --border-tech: 1px solid rgba(139, 92, 246, 0.3);
  --border-neon: 1px solid rgba(0, 255, 255, 0.5);
  --border-glow: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== ENHANCED BUTTON STYLES ===== */
.btn-gaming {
  @apply relative overflow-hidden px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: var(--gradient-tech);
  border: var(--border-tech);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.btn-gaming:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-neon);
  border-color: var(--neon-purple);
}

.btn-gaming::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-cyber);
  transition: left 0.5s ease;
  z-index: -1;
}

.btn-gaming:hover::before {
  left: 0;
}

/* ===== AUTH FORM BUTTON (NO NEON EFFECTS) ===== */
.btn-auth {
  @apply relative overflow-hidden px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: var(--gradient-tech);
  border: var(--border-tech);
  color: #ffffff;
  /* Removed text-shadow for cleaner look */
}

.btn-auth:hover {
  transform: translateY(-2px);
  /* Removed neon box-shadow, using subtle shadow instead */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  /* Removed neon border color, using subtle purple instead */
  border-color: rgba(139, 92, 246, 0.7);
}

.btn-auth::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-tech); /* Using same gradient as base for consistency */
  transition: left 0.5s ease;
  z-index: -1;
}

.btn-auth:hover::before {
  left: 0;
}

/* ===== ENHANCED GOOGLE SIGN-IN BUTTON ===== */
.btn-google-enhanced {
  @apply relative overflow-hidden px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.btn-google-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(66, 133, 244, 0.2);
}

.btn-google-enhanced:active {
  transform: translateY(-1px) scale(1.01);
}

.btn-google-enhanced .google-icon {
  transition: transform 0.3s ease;
}

.btn-google-enhanced:hover .google-icon {
  transform: scale(1.1) rotate(5deg);
}

.btn-google-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.btn-google-enhanced:hover::before {
  left: 100%;
}

.btn-neon {
  @apply relative px-6 py-3 rounded-lg font-medium transition-all duration-300;
  background: transparent;
  border: 2px solid var(--neon-cyan);
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
}

.btn-neon:hover {
  background: rgba(0, 255, 255, 0.1);
  box-shadow: var(--shadow-cyber);
  transform: scale(1.05);
}

.btn-cyber {
  @apply relative px-6 py-3 rounded-lg font-bold transition-all duration-300;
  background: var(--gradient-neon);
  border: none;
  color: #000000;
  text-shadow: none;
  animation: pulse-neon 2s infinite;
}

.btn-cyber:hover {
  animation: none;
  transform: scale(1.1);
  box-shadow: 0 0 40px rgba(255, 0, 255, 0.6);
}

/* ===== CARD ENHANCEMENTS ===== */
.card-gaming {
  @apply relative rounded-xl overflow-hidden transition-all duration-300;
  background: rgba(17, 24, 39, 0.9);
  border: var(--border-glow);
  backdrop-filter: blur(10px);
}

.card-gaming:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-glow);
  border-color: var(--neon-purple);
}

.card-gaming::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-cyber);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-gaming:hover::before {
  opacity: 1;
}

.card-neon {
  @apply relative rounded-xl p-6 transition-all duration-300;
  background: rgba(17, 24, 39, 0.95);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.card-neon::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--gradient-cyber);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

.card-neon:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-neon);
}

/* ===== TECH GRID PATTERNS ===== */
.tech-grid {
  background-image: 
    linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.circuit-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 2px, transparent 2px);
  background-size: 40px 40px;
}

/* ===== NEON TEXT EFFECTS ===== */
.text-neon {
  color: var(--neon-cyan);
  text-shadow: 
    0 0 5px var(--neon-cyan),
    0 0 10px var(--neon-cyan),
    0 0 15px var(--neon-cyan);
}

.text-neon-purple {
  color: var(--neon-purple);
  text-shadow: 
    0 0 5px var(--neon-purple),
    0 0 10px var(--neon-purple),
    0 0 15px var(--neon-purple);
}

.text-cyber {
  background: var(--gradient-cyber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes pulse-neon {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
  }
}

@keyframes glow-rotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

@keyframes tech-scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes data-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-glow {
  animation: pulse-neon 2s ease-in-out infinite;
}

.animate-glow-rotate {
  animation: glow-rotate 3s linear infinite;
}

.animate-tech-scan {
  animation: tech-scan 2s ease-in-out infinite;
}

.animate-data-flow {
  background-size: 200% 200%;
  animation: data-flow 3s ease infinite;
}

/* ===== ENHANCED FORM ELEMENTS ===== */
.input-gaming {
  @apply w-full rounded-lg transition-all duration-300;
  background: rgba(17, 24, 39, 0.8);
  border: var(--border-tech);
  color: #ffffff; /* Changed from #ff6600 to white for better readability */
  backdrop-filter: blur(5px);
  padding: 12px 16px;
  min-height: 48px; /* Ensure proper touch target */
  font-size: 16px; /* Prevent zoom on iOS */
  line-height: 1.5;
}

.input-gaming:focus {
  outline: none;
  border-color: var(--neon-purple);
  box-shadow: var(--shadow-neon);
  background: rgba(17, 24, 39, 0.95);
  transform: translateY(-1px);
}

.input-gaming:hover:not(:focus) {
  border-color: rgba(139, 92, 246, 0.5);
  background: rgba(17, 24, 39, 0.85);
}

.input-gaming::placeholder {
  color: rgba(255, 255, 255, 0.5); /* Changed from orange to white with opacity for better readability */
  font-size: 14px;
}

/* Error state for input-gaming */
.input-gaming.error {
  border-color: rgba(239, 68, 68, 0.5);
  background: rgba(17, 24, 39, 0.8);
}

.input-gaming.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

/* ===== ENHANCED NAVIGATION ===== */
.nav-gaming {
  @apply relative transition-all duration-300;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: var(--border-tech);
}

.nav-gaming::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-cyber);
  opacity: 0.5;
}

.nav-link-gaming {
  @apply relative px-4 py-2 rounded-lg transition-all duration-300;
  color: rgba(255, 255, 255, 0.8);
}

.nav-link-gaming:hover {
  color: var(--neon-cyan);
  background: rgba(139, 92, 246, 0.1);
  text-shadow: 0 0 10px var(--neon-cyan);
}

.nav-link-gaming.active {
  color: var(--neon-purple);
  background: rgba(139, 92, 246, 0.2);
  text-shadow: 0 0 10px var(--neon-purple);
}

/* ===== ENHANCED MODALS ===== */
.modal-gaming {
  @apply relative rounded-xl overflow-hidden;
  background: rgba(17, 24, 39, 0.95);
  border: var(--border-tech);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-tech);
}

.modal-gaming::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-cyber);
}

/* ===== ENHANCED TOOLTIPS ===== */
.tooltip-gaming {
  @apply relative;
}

.tooltip-gaming::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 12px;
  background: rgba(17, 24, 39, 0.95);
  border: var(--border-neon);
  border-radius: 8px;
  color: var(--neon-cyan);
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.tooltip-gaming:hover::after {
  opacity: 1;
}

/* ===== ENHANCED PROGRESS BARS ===== */
.progress-gaming {
  @apply w-full h-2 rounded-full overflow-hidden;
  background: rgba(17, 24, 39, 0.8);
  border: var(--border-tech);
}

.progress-gaming-fill {
  @apply h-full rounded-full transition-all duration-500;
  background: var(--gradient-cyber);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
  .btn-gaming,
  .btn-neon,
  .btn-cyber {
    @apply px-4 py-2 text-sm;
  }
  
  .card-gaming,
  .card-neon {
    @apply p-4;
  }
  
  .text-neon,
  .text-neon-purple {
    text-shadow: 
      0 0 3px currentColor,
      0 0 6px currentColor;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .animate-glow,
  .animate-glow-rotate,
  .animate-tech-scan,
  .animate-data-flow {
    animation: none;
  }
  
  .btn-gaming,
  .btn-neon,
  .btn-cyber,
  .card-gaming,
  .card-neon {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  :root {
    --neon-cyan: #ffffff;
    --neon-purple: #ffffff;
    --border-tech: 2px solid #ffffff;
    --border-neon: 2px solid #ffffff;
  }
}
