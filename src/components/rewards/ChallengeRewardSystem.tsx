/**
 * Challenge-Based Rewards System
 * 
 * Dynamic reward system for community challenges with tier-based multipliers,
 * special prizes, and comprehensive reward management.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Trophy, 
  Award, 
  Star, 
  Crown, 
  Target, 
  Gift,
  Zap,
  Flame,
  Gem,
  Coins,
  Medal,
  Calendar,
  Clock,
  TrendingUp,
  Users,
  Eye,
  Heart,
  MessageSquare,
  Share2,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertCircle,
  Info,
  Plus,
  Minus,
  Settings,
  Filter,
  Search,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Rocket,
  Shield,
  Lock,
  Unlock
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useUser } from '@/lib/useUser'
import { useAccessibility } from '@/components/accessibility/AccessibilityProvider'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'

interface ChallengeReward {
  id: string
  challengeId: string
  challengeTitle: string
  type: 'points' | 'badge' | 'item' | 'privilege' | 'cosmetic' | 'currency' | 'experience'
  category: 'participation' | 'placement' | 'achievement' | 'milestone' | 'bonus' | 'special'
  tier: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond' | 'legendary'
  name: string
  description: string
  icon: string
  iconColor: string
  value: number
  multiplier: number
  baseValue: number
  conditions: {
    placement?: number // 1st, 2nd, 3rd place
    threshold?: number // minimum score/votes needed
    participation?: boolean // just for participating
    streak?: number // consecutive challenges
    special?: string // special conditions
  }
  eligibility: {
    userTier: string[]
    minLevel: number
    maxClaims?: number
    timeWindow?: number // hours
    prerequisites?: string[]
  }
  metadata: {
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary' | 'mythic'
    transferable: boolean
    stackable: boolean
    expiresAt?: Date
    seasonalOnly?: boolean
    limitedEdition?: boolean
  }
  claimedBy: {
    userId: string
    userName: string
    userAvatar?: string
    claimedAt: Date
    finalValue: number
  }[]
  isActive: boolean
  isClaimable: boolean
  totalClaimed: number
  maxClaims?: number
}

interface RewardTier {
  id: string
  name: string
  level: number
  multiplier: number
  color: string
  icon: React.ComponentType
  requirements: {
    totalPoints: number
    challengesCompleted: number
    winRate: number
    communityScore: number
  }
  benefits: {
    rewardMultiplier: number
    exclusiveRewards: boolean
    priorityAccess: boolean
    specialBadges: boolean
    customization: boolean
  }
}

interface UserRewardStats {
  totalRewards: number
  totalValue: number
  currentTier: string
  nextTier?: string
  tierProgress: number
  recentRewards: ChallengeReward[]
  topCategories: { category: string; count: number; value: number }[]
  streakBonus: number
  multiplierActive: boolean
  pendingClaims: ChallengeReward[]
}

export default function ChallengeRewardSystem() {
  const { user } = useUser()
  const { announce } = useAccessibility()
  
  // State
  const [activeTab, setActiveTab] = useState('overview')
  const [rewards, setRewards] = useState<ChallengeReward[]>([])
  const [tiers, setTiers] = useState<RewardTier[]>([])
  const [userStats, setUserStats] = useState<UserRewardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedTier, setSelectedTier] = useState('all')
  const [showClaimedOnly, setShowClaimedOnly] = useState(false)

  // Load data
  useEffect(() => {
    loadRewards()
    loadTiers()
    loadUserStats()
  }, [])

  const loadRewards = async () => {
    setLoading(true)
    try {
      // Mock rewards data - replace with actual API call
      const mockRewards: ChallengeReward[] = [
        {
          id: '1',
          challengeId: 'cyberpunk-2024',
          challengeTitle: 'Cyberpunk Design Challenge',
          type: 'points',
          category: 'placement',
          tier: 'gold',
          name: 'Champion Points',
          description: 'Bonus points for winning the Cyberpunk Design Challenge',
          icon: '🏆',
          iconColor: 'text-yellow-400',
          value: 2500,
          multiplier: 2.0,
          baseValue: 1250,
          conditions: {
            placement: 1,
            threshold: 100
          },
          eligibility: {
            userTier: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
            minLevel: 1
          },
          metadata: {
            rarity: 'legendary',
            transferable: false,
            stackable: false,
            limitedEdition: true
          },
          claimedBy: [
            {
              userId: 'user1',
              userName: 'Alex Rodriguez',
              userAvatar: '/avatars/user1.jpg',
              claimedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              finalValue: 2500
            }
          ],
          isActive: true,
          isClaimable: false,
          totalClaimed: 1,
          maxClaims: 1
        },
        {
          id: '2',
          challengeId: 'weekly-build',
          challengeTitle: 'Weekly Build Challenge',
          type: 'badge',
          category: 'participation',
          tier: 'silver',
          name: 'Consistent Builder',
          description: 'Participate in 5 consecutive weekly challenges',
          icon: '🔨',
          iconColor: 'text-blue-400',
          value: 500,
          multiplier: 1.5,
          baseValue: 333,
          conditions: {
            streak: 5,
            participation: true
          },
          eligibility: {
            userTier: ['bronze', 'silver', 'gold', 'platinum', 'diamond'],
            minLevel: 5,
            timeWindow: 168 // 1 week
          },
          metadata: {
            rarity: 'rare',
            transferable: false,
            stackable: true
          },
          claimedBy: [
            {
              userId: 'user2',
              userName: 'Sarah Chen',
              claimedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
              finalValue: 500
            },
            {
              userId: 'user3',
              userName: 'Mike Johnson',
              claimedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
              finalValue: 750 // higher tier multiplier
            }
          ],
          isActive: true,
          isClaimable: true,
          totalClaimed: 2
        },
        {
          id: '3',
          challengeId: 'artisan-showcase',
          challengeTitle: 'Artisan Keycap Showcase',
          type: 'item',
          category: 'special',
          tier: 'platinum',
          name: 'Exclusive Artisan Set',
          description: 'Limited edition artisan keycap set for showcase winners',
          icon: '💎',
          iconColor: 'text-purple-400',
          value: 5000,
          multiplier: 3.0,
          baseValue: 1667,
          conditions: {
            placement: 1,
            threshold: 200,
            special: 'community_choice'
          },
          eligibility: {
            userTier: ['gold', 'platinum', 'diamond'],
            minLevel: 10,
            maxClaims: 3
          },
          metadata: {
            rarity: 'mythic',
            transferable: true,
            stackable: false,
            limitedEdition: true,
            expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          },
          claimedBy: [],
          isActive: true,
          isClaimable: true,
          totalClaimed: 0,
          maxClaims: 3
        }
      ]
      
      setRewards(mockRewards)
    } catch (error) {
      console.error('Failed to load rewards:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTiers = async () => {
    try {
      // Mock tier data - replace with actual API call
      const mockTiers: RewardTier[] = [
        {
          id: 'bronze',
          name: 'Bronze',
          level: 1,
          multiplier: 1.0,
          color: 'text-orange-600',
          icon: Medal,
          requirements: {
            totalPoints: 0,
            challengesCompleted: 0,
            winRate: 0,
            communityScore: 0
          },
          benefits: {
            rewardMultiplier: 1.0,
            exclusiveRewards: false,
            priorityAccess: false,
            specialBadges: false,
            customization: false
          }
        },
        {
          id: 'silver',
          name: 'Silver',
          level: 2,
          multiplier: 1.2,
          color: 'text-gray-400',
          icon: Award,
          requirements: {
            totalPoints: 1000,
            challengesCompleted: 5,
            winRate: 0.2,
            communityScore: 50
          },
          benefits: {
            rewardMultiplier: 1.2,
            exclusiveRewards: false,
            priorityAccess: false,
            specialBadges: true,
            customization: false
          }
        },
        {
          id: 'gold',
          name: 'Gold',
          level: 3,
          multiplier: 1.5,
          color: 'text-yellow-400',
          icon: Trophy,
          requirements: {
            totalPoints: 5000,
            challengesCompleted: 15,
            winRate: 0.4,
            communityScore: 150
          },
          benefits: {
            rewardMultiplier: 1.5,
            exclusiveRewards: true,
            priorityAccess: false,
            specialBadges: true,
            customization: true
          }
        },
        {
          id: 'platinum',
          name: 'Platinum',
          level: 4,
          multiplier: 2.0,
          color: 'text-cyan-400',
          icon: Gem,
          requirements: {
            totalPoints: 15000,
            challengesCompleted: 30,
            winRate: 0.6,
            communityScore: 300
          },
          benefits: {
            rewardMultiplier: 2.0,
            exclusiveRewards: true,
            priorityAccess: true,
            specialBadges: true,
            customization: true
          }
        },
        {
          id: 'diamond',
          name: 'Diamond',
          level: 5,
          multiplier: 3.0,
          color: 'text-blue-400',
          icon: Crown,
          requirements: {
            totalPoints: 50000,
            challengesCompleted: 50,
            winRate: 0.8,
            communityScore: 500
          },
          benefits: {
            rewardMultiplier: 3.0,
            exclusiveRewards: true,
            priorityAccess: true,
            specialBadges: true,
            customization: true
          }
        }
      ]
      
      setTiers(mockTiers)
    } catch (error) {
      console.error('Failed to load tiers:', error)
    }
  }

  const loadUserStats = async () => {
    try {
      // Mock user stats - replace with actual API call
      const mockStats: UserRewardStats = {
        totalRewards: 23,
        totalValue: 12750,
        currentTier: 'gold',
        nextTier: 'platinum',
        tierProgress: 65,
        recentRewards: rewards.slice(0, 3),
        topCategories: [
          { category: 'participation', count: 8, value: 3200 },
          { category: 'placement', count: 5, value: 6500 },
          { category: 'achievement', count: 4, value: 2000 },
          { category: 'bonus', count: 6, value: 1050 }
        ],
        streakBonus: 1.25,
        multiplierActive: true,
        pendingClaims: rewards.filter(r => r.isClaimable)
      }
      
      setUserStats(mockStats)
    } catch (error) {
      console.error('Failed to load user stats:', error)
    }
  }

  // Filter rewards
  const filteredRewards = useMemo(() => {
    let filtered = rewards

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(reward =>
        reward.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        reward.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        reward.challengeTitle.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(reward => reward.category === selectedCategory)
    }

    // Tier filter
    if (selectedTier !== 'all') {
      filtered = filtered.filter(reward => reward.tier === selectedTier)
    }

    // Claimed filter
    if (showClaimedOnly) {
      filtered = filtered.filter(reward => reward.totalClaimed > 0)
    }

    return filtered
  }, [rewards, searchQuery, selectedCategory, selectedTier, showClaimedOnly])

  const handleClaimReward = useCallback(async (rewardId: string) => {
    setRewards(prev => prev.map(reward => {
      if (reward.id === rewardId && reward.isClaimable) {
        const currentTier = tiers.find(t => t.id === userStats?.currentTier)
        const finalValue = Math.round(reward.baseValue * (currentTier?.multiplier || 1) * (userStats?.streakBonus || 1))
        
        return {
          ...reward,
          claimedBy: [
            ...reward.claimedBy,
            {
              userId: user?.uid || 'current-user',
              userName: user?.displayName || 'Current User',
              userAvatar: user?.photoURL || undefined,
              claimedAt: new Date(),
              finalValue
            }
          ],
          totalClaimed: reward.totalClaimed + 1,
          isClaimable: reward.maxClaims ? reward.totalClaimed + 1 < reward.maxClaims : true
        } as ChallengeReward
      }
      return reward
    }))
    
    announce('Reward claimed successfully!')
  }, [user, tiers, userStats, announce])

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-white">Challenge Rewards</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Earn dynamic rewards through community challenges with tier-based multipliers and special prizes
        </p>
      </div>

      {/* User Stats Overview */}
      {userStats && (
        <RewardStatsOverview stats={userStats} tiers={tiers} />
      )}

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 bg-gray-900/50">
          <TabsTrigger value="overview" className="data-[state=active]:bg-accent-600">
            <Trophy className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="rewards" className="data-[state=active]:bg-accent-600">
            <Gift className="w-4 h-4 mr-2" />
            Rewards
          </TabsTrigger>
          <TabsTrigger value="tiers" className="data-[state=active]:bg-accent-600">
            <Crown className="w-4 h-4 mr-2" />
            Tiers
          </TabsTrigger>
          <TabsTrigger value="leaderboard" className="data-[state=active]:bg-accent-600">
            <BarChart3 className="w-4 h-4 mr-2" />
            Leaderboard
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-accent-600">
            <PieChart className="w-4 h-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6 mt-6">
          <RewardOverview
            rewards={rewards}
            userStats={userStats}
            tiers={tiers}
            onClaimReward={handleClaimReward}
          />
        </TabsContent>

        {/* Rewards Tab */}
        <TabsContent value="rewards" className="space-y-6 mt-6">
          <RewardFilters
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            selectedTier={selectedTier}
            onTierChange={setSelectedTier}
            showClaimedOnly={showClaimedOnly}
            onShowClaimedChange={setShowClaimedOnly}
          />
          
          <RewardGrid
            rewards={filteredRewards}
            loading={loading}
            onClaimReward={handleClaimReward}
            userStats={userStats}
            tiers={tiers}
          />
        </TabsContent>

        {/* Other tabs content will be added in the next part */}
      </Tabs>
    </div>
  )
}

/**
 * Reward Stats Overview Component
 */
interface RewardStatsOverviewProps {
  stats: UserRewardStats
  tiers: RewardTier[]
}

function RewardStatsOverview({ stats, tiers }: RewardStatsOverviewProps) {
  const currentTier = tiers.find(t => t.id === stats.currentTier)
  const nextTier = tiers.find(t => t.id === stats.nextTier)
  const CurrentTierIcon = currentTier?.icon || Trophy

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{stats.totalRewards}</div>
          <div className="text-xs text-gray-400">Total Rewards</div>
          <div className="text-xs text-green-400 mt-1">+3 this week</div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{stats.totalValue.toLocaleString()}</div>
          <div className="text-xs text-gray-400">Total Value</div>
          <div className="text-xs text-accent-400 mt-1">Points</div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="flex items-center justify-center mb-1">
            <CurrentTierIcon className={`w-6 h-6 ${currentTier?.color || 'text-gray-400'}`} />
          </div>
          <div className="text-xs text-gray-400">Current Tier</div>
          <div className={`text-xs mt-1 ${currentTier?.color || 'text-gray-400'}`}>
            {currentTier?.name || 'Unknown'}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{stats.tierProgress}%</div>
          <div className="text-xs text-gray-400">Tier Progress</div>
          <div className="text-xs text-blue-400 mt-1">
            To {nextTier?.name || 'Max'}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{stats.streakBonus.toFixed(1)}x</div>
          <div className="text-xs text-gray-400">Streak Bonus</div>
          <div className="text-xs text-orange-400 mt-1">
            {stats.multiplierActive ? '🔥 Active' : 'Inactive'}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-900/50 border-gray-700">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-white mb-1">{stats.pendingClaims.length}</div>
          <div className="text-xs text-gray-400">Pending Claims</div>
          <div className="text-xs text-purple-400 mt-1">Available</div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Reward Overview Component
 */
interface RewardOverviewProps {
  rewards: ChallengeReward[]
  userStats: UserRewardStats | null
  tiers: RewardTier[]
  onClaimReward: (rewardId: string) => void
}

function RewardOverview({ rewards, userStats, tiers, onClaimReward }: RewardOverviewProps) {
  const pendingRewards = rewards.filter(r => r.isClaimable)
  const recentRewards = rewards.filter(r => r.totalClaimed > 0).slice(0, 5)

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Pending Claims */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Gift className="w-5 h-5 mr-2" />
            Pending Claims ({pendingRewards.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {pendingRewards.length > 0 ? (
            pendingRewards.map(reward => (
              <div key={reward.id} className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{reward.icon}</div>
                  <div>
                    <h4 className="font-semibold text-white">{reward.name}</h4>
                    <p className="text-sm text-gray-400">{reward.challengeTitle}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getTierColor(reward.tier)}>
                        {reward.tier}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        +{calculateFinalValue(reward, userStats, tiers)} points
                      </span>
                    </div>
                  </div>
                </div>
                <Button
                  onClick={() => onClaimReward(reward.id)}
                  className="bg-accent-600 hover:bg-accent-700 text-white"
                  size="sm"
                >
                  Claim
                </Button>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-400 py-8">
              <Gift className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No pending rewards</p>
              <p className="text-sm">Complete challenges to earn rewards!</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Rewards */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Clock className="w-5 h-5 mr-2" />
            Recent Rewards
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {recentRewards.length > 0 ? (
            recentRewards.map(reward => (
              <div key={reward.id} className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
                <div className="text-2xl">{reward.icon}</div>
                <div className="flex-1">
                  <h4 className="font-semibold text-white">{reward.name}</h4>
                  <p className="text-sm text-gray-400">{reward.challengeTitle}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge className={getTierColor(reward.tier)}>
                      {reward.tier}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {reward.claimedBy.length > 0 && formatDistanceToNow(
                        reward.claimedBy[reward.claimedBy.length - 1].claimedAt,
                        { addSuffix: true, locale: id }
                      )}
                    </span>
                  </div>
                </div>
                <div className="text-accent-400 font-semibold">
                  +{reward.claimedBy[reward.claimedBy.length - 1]?.finalValue || reward.value}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-400 py-8">
              <Clock className="w-12 h-12 mx-auto mb-3 opacity-50" />
              <p>No recent rewards</p>
              <p className="text-sm">Start participating in challenges!</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      <Card className="bg-gray-900/50 border-gray-700 lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <PieChart className="w-5 h-5 mr-2" />
            Reward Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {userStats?.topCategories.map(category => (
              <div key={category.category} className="p-4 bg-gray-800/50 rounded-lg text-center">
                <div className="text-2xl font-bold text-white mb-1">{category.count}</div>
                <div className="text-sm text-gray-400 mb-2 capitalize">{category.category}</div>
                <div className="text-xs text-accent-400">{category.value.toLocaleString()} pts</div>
                <Progress
                  value={(category.count / (userStats?.totalRewards || 1)) * 100}
                  className="h-1 mt-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Reward Filters Component
 */
interface RewardFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  selectedCategory: string
  onCategoryChange: (category: string) => void
  selectedTier: string
  onTierChange: (tier: string) => void
  showClaimedOnly: boolean
  onShowClaimedChange: (show: boolean) => void
}

function RewardFilters({
  searchQuery,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  selectedTier,
  onTierChange,
  showClaimedOnly,
  onShowClaimedChange
}: RewardFiltersProps) {
  return (
    <Card className="bg-gray-900/50 border-gray-700">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search rewards..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 text-white rounded-md focus:ring-2 focus:ring-accent-500"
            />
          </div>

          <div className="flex items-center space-x-3">
            <select
              value={selectedCategory}
              onChange={(e) => onCategoryChange(e.target.value)}
              className="bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2"
            >
              <option value="all">All Categories</option>
              <option value="participation">Participation</option>
              <option value="placement">Placement</option>
              <option value="achievement">Achievement</option>
              <option value="milestone">Milestone</option>
              <option value="bonus">Bonus</option>
              <option value="special">Special</option>
            </select>

            <select
              value={selectedTier}
              onChange={(e) => onTierChange(e.target.value)}
              className="bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2"
            >
              <option value="all">All Tiers</option>
              <option value="bronze">Bronze</option>
              <option value="silver">Silver</option>
              <option value="gold">Gold</option>
              <option value="platinum">Platinum</option>
              <option value="diamond">Diamond</option>
              <option value="legendary">Legendary</option>
            </select>

            <label className="flex items-center space-x-2 text-gray-300">
              <input
                type="checkbox"
                checked={showClaimedOnly}
                onChange={(e) => onShowClaimedChange(e.target.checked)}
                className="rounded border-gray-600 text-accent-600 focus:ring-accent-500"
              />
              <span className="text-sm">Claimed only</span>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Helper functions
const getTierColor = (tier: string): string => {
  const colors = {
    bronze: 'tier-bronze-text tier-bronze-bg',
    silver: 'tier-silver-text tier-silver-bg',
    gold: 'tier-gold-text tier-gold-bg',
    platinum: 'tier-platinum-text tier-platinum-bg',
    diamond: 'tier-diamond-text tier-diamond-bg',
    legendary: 'text-purple-400 bg-purple-400/20'
  }
  return colors[tier as keyof typeof colors] || 'text-gray-400 bg-gray-400/20'
}

const calculateFinalValue = (
  reward: ChallengeReward,
  userStats: UserRewardStats | null,
  tiers: RewardTier[]
): number => {
  const currentTier = tiers.find(t => t.id === userStats?.currentTier)
  const tierMultiplier = currentTier?.multiplier || 1
  const streakBonus = userStats?.streakBonus || 1

  return Math.round(reward.baseValue * tierMultiplier * streakBonus)
}

// RewardGrid Component
interface RewardGridProps {
  rewards: ChallengeReward[]
  loading: boolean
  onClaimReward: (rewardId: string) => void
  userStats: UserRewardStats | null
  tiers: RewardTier[]
}

function RewardGrid({ rewards, loading, onClaimReward, userStats, tiers }: RewardGridProps) {
  if (loading) {
    return <div className="text-center py-8 text-gray-400">Loading rewards...</div>
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {rewards.map((reward) => (
        <Card key={reward.id} className="bg-gray-900/50 border-gray-700">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-white font-semibold">{reward.name}</h3>
              <p className="text-gray-400 text-sm mt-2">{reward.description}</p>
              <Button 
                onClick={() => onClaimReward(reward.id)}
                className="mt-4"
                disabled={!reward.isClaimable}
              >
                Claim Reward
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
